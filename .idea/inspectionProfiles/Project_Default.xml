<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="6">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="quantstats" />
            <item index="2" class="java.lang.String" itemvalue="yfinance" />
            <item index="3" class="java.lang.String" itemvalue="backtrader" />
            <item index="4" class="java.lang.String" itemvalue="schemdraw" />
            <item index="5" class="java.lang.String" itemvalue="matplotlib" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>
# =====================================================
# QuantMCP 环境配置文件
# 请根据你的实际环境修改下面的配置参数
# =====================================================

# 服务器配置
QUANTMCP_HOST=127.0.0.1          # MCP服务器监听地址，通常保持127.0.0.1
QUANTMCP_PORT=8000               # MCP服务器端口号，可根据需要修改
QUANTMCP_TRANSPORT=sse           # 传输协议，保持sse即可

# XTQuant/QMT 交易客户端配置
# 重要：请修改为你的QMT安装路径和账户信息
QMT_PATH=你的QMT安装路径\userdata_mini        # QMT客户端数据目录，如: D:\QMT\userdata_mini
QMT_SESSION_ID=你的会话ID                      # QMT会话ID，整数，如: 12345
QMT_ACCOUNT_ID=你的交易账户ID                  # 你的模拟或实盘交易账户ID

# QMT策略保存目录
QMT_STRATEGY_DIR=你的QMT策略目录\mpython       # QMT策略文件保存目录，如: D:\QMT\mpython

# 交易风险控制配置
MAX_ORDER_VALUE=100000.0         # 单笔订单最大金额(元)，防止误操作大额下单
MAX_POSITION_VALUE=500000.0      # 单只股票最大持仓金额(元)，控制单股风险
MIN_ORDER_QUANTITY=100           # 最小下单数量(股)，通常为100的整数倍
MARKET_ORDER_SPREAD=0.1          # 市价单价差比例(0.1=10%)，避免成交价偏离过大

# 策略默认参数配置
DEFAULT_SYMBOL=000001.SZ         # 默认股票代码，用于测试和演示
DEFAULT_START_DATE=********      # 默认回测开始日期，格式YYYYMMDD
DEFAULT_END_DATE=********        # 默认回测结束日期，格式YYYYMMDD  
DEFAULT_SHORT_PERIOD=5           # 默认短期均线天数
DEFAULT_LONG_PERIOD=20           # 默认长期均线天数

# 日志配置
LOG_LEVEL=INFO                   # 日志级别: DEBUG/INFO/WARNING/ERROR
LOG_FILE=logs/quantmcp.log       # 日志文件路径
